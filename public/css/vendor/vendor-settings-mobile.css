/* ===== NATIVE MOBILE APP SETTINGS ===== */
/* iOS/Android Style Design with Green Theme */

/* Hide mobile view on desktop */
@media (min-width: 769px) {
    .mobile-settings-app {
        display: none !important;
    }
}

/* Hide desktop version on mobile screens */
@media (max-width: 768px) {
    .settings-container {
        display: none !important;
    }
}

/* Default: Hide mobile view until media query activates */
.mobile-settings-app {
    display: none;
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;

    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;

    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;

    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;

    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        margin: 0;
        padding: 0;
    }

    html, body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background: var(--background);
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.5;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* ===== NATIVE APP LAYOUT ===== */
    .mobile-settings-app {
        display: block !important;
        min-height: 100vh;
        background: var(--background);
        padding-top: var(--safe-area-top);
        padding-bottom: var(--safe-area-bottom);
        position: relative;
    }

    /* ===== MOBILE HEADER ===== */
    .mobile-settings-header {
        background: var(--white);
        padding: var(--spacing-lg);
        padding-top: calc(var(--spacing-lg) + var(--safe-area-top));
        border-bottom: 1px solid var(--border);
        text-align: center;
        box-shadow: var(--shadow-sm);
        position: sticky;
        top: 0;
        z-index: 100;
    }

    .mobile-settings-title {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }



    /* ===== MOBILE SETTINGS CONTENT ===== */
    .mobile-settings-content {
        padding: 0 var(--spacing-lg) var(--spacing-lg);
    }

    .mobile-settings-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        overflow: hidden;
        border: 1px solid var(--border-light);
    }

    .mobile-card-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        background: var(--surface-secondary);
    }

    .mobile-card-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-base);
    }

    .mobile-card-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .mobile-card-body {
        padding: 0;
    }

    /* ===== MOBILE SETTING ITEMS ===== */
    .mobile-setting-item {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        display: block;
    }

    .mobile-setting-item:last-child {
        border-bottom: none;
    }

    .mobile-setting-info {
        margin-bottom: var(--spacing-md);
    }

    .mobile-setting-name {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .mobile-setting-description {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        line-height: 1.4;
    }

    .mobile-setting-control {
        width: 100%;
    }

    /* ===== MOBILE FORM STYLES ===== */
    .mobile-form-input,
    .mobile-form-select,
    .mobile-form-textarea {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        border: 2px solid var(--border);
        border-radius: var(--radius-md);
        font-size: var(--text-base);
        background: var(--white);
        color: var(--text-primary);
        -webkit-appearance: none;
        appearance: none;
        transition: all 0.2s ease;
    }

    .mobile-form-input:focus,
    .mobile-form-select:focus,
    .mobile-form-textarea:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    .mobile-form-textarea {
        min-height: 100px;
        resize: vertical;
    }

    .mobile-form-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right var(--spacing-md) center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: calc(var(--spacing-lg) + 24px);
    }

    /* ===== MOBILE LOGO UPLOAD ===== */
    .mobile-logo-upload-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 120px;
    }

    .mobile-logo-preview {
        position: relative;
        display: inline-block;
    }

    .mobile-logo-image {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-md);
        object-fit: cover;
        border: 2px solid var(--border);
    }

    .mobile-logo-remove-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 24px;
        height: 24px;
        background: var(--error);
        color: var(--white);
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-xs);
        cursor: pointer;
    }

    .mobile-logo-upload {
        text-align: center;
    }

    .mobile-upload-label {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-xl);
        border: 2px dashed var(--border);
        border-radius: var(--radius-md);
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .mobile-upload-label:hover {
        border-color: var(--primary);
        color: var(--primary);
    }

    .mobile-upload-label i {
        font-size: var(--text-2xl);
    }

    .visually-hidden {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    /* ===== MOBILE BUTTONS ===== */
    .mobile-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        border-radius: var(--radius-md);
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--text-base);
        font-weight: 600;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        min-height: 48px;
    }

    .mobile-btn-primary {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        color: var(--white);
        box-shadow: var(--shadow-md);
    }

    .mobile-btn-primary:active {
        transform: scale(0.98);
        box-shadow: var(--shadow-sm);
    }

    .mobile-btn-secondary {
        background: var(--white);
        color: var(--text-secondary);
        border-color: var(--border);
    }

    .mobile-btn-secondary:active {
        background: var(--surface-secondary);
    }

    .mobile-btn-outline {
        background: transparent;
        color: var(--primary);
        border-color: var(--primary);
    }

    .mobile-btn-outline:active {
        background: var(--primary-bg);
    }

    /* ===== MOBILE ACTIONS FOOTER ===== */
    .mobile-settings-actions {
        background: var(--white);
        padding: var(--spacing-lg);
        border-top: 1px solid var(--border);
        display: flex;
        gap: var(--spacing-md);
        position: sticky;
        bottom: 0;
        margin-top: auto;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    }

    .mobile-settings-actions .mobile-btn {
        flex: 1;
    }

    /* ===== MOBILE GATEWAY NOTICE ===== */
    .mobile-gateway-notice {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
        background: var(--primary-bg);
        border-radius: var(--radius-md);
        margin: var(--spacing-lg);
        color: var(--primary-text);
    }

    .mobile-gateway-notice i {
        font-size: var(--text-lg);
        color: var(--primary);
    }

    .mobile-gateway-notice p {
        margin: 0;
        font-size: var(--text-sm);
        font-weight: 500;
    }

    /* ===== MOBILE THEME MODAL ===== */
    .mobile-theme-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: flex-end;
        z-index: 1000;
        backdrop-filter: blur(4px);
    }

    .mobile-modal-container {
        background: var(--white);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        width: 100%;
        max-height: 80vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        animation: slideUp 0.3s ease-out;
    }

    @keyframes slideUp {
        from {
            transform: translateY(100%);
        }
        to {
            transform: translateY(0);
        }
    }

    .mobile-modal-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: var(--surface-secondary);
    }

    .mobile-modal-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .mobile-modal-close {
        width: 32px;
        height: 32px;
        background: var(--surface);
        border: 1px solid var(--border);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-secondary);
        cursor: pointer;
    }

    .mobile-modal-body {
        flex: 1;
        overflow-y: auto;
        padding: var(--spacing-lg);
    }

    .mobile-modal-footer {
        padding: var(--spacing-lg);
        border-top: 1px solid var(--border);
        background: var(--surface-secondary);
    }

    /* ===== MOBILE THEME FILTER ===== */
    .mobile-theme-filter {
        margin-bottom: var(--spacing-lg);
    }

    .mobile-filter-chips {
        display: flex;
        gap: var(--spacing-sm);
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }

    .mobile-filter-chip {
        flex-shrink: 0;
        padding: var(--spacing-sm) var(--spacing-lg);
        background: var(--surface-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        font-size: var(--text-sm);
        font-weight: 500;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .mobile-filter-chip.active {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }

    /* ===== MOBILE THEME GRID ===== */
    .mobile-theme-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }

    .mobile-theme-card {
        background: var(--white);
        border: 1px solid var(--border);
        border-radius: var(--radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .mobile-theme-preview {
        position: relative;
        aspect-ratio: 16/10;
        overflow: hidden;
    }

    .mobile-theme-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .mobile-theme-badge {
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        background: var(--warning);
        color: var(--white);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--text-xs);
        font-weight: 600;
    }

    .mobile-theme-info {
        padding: var(--spacing-md);
    }

    .mobile-theme-name {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .mobile-theme-meta {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-xs);
        color: var(--text-tertiary);
    }

    .mobile-theme-category {
        text-transform: capitalize;
    }

    .mobile-theme-rating {
        display: flex;
        align-items: center;
        gap: 2px;
    }

    .mobile-theme-rating i {
        color: var(--warning);
    }

    .mobile-theme-buttons {
        padding: var(--spacing-md);
        display: flex;
        gap: var(--spacing-sm);
    }

    .mobile-theme-btn {
        flex: 1;
        padding: var(--spacing-sm);
        border: 1px solid var(--border);
        border-radius: var(--radius-sm);
        background: var(--white);
        color: var(--text-secondary);
        font-size: var(--text-xs);
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
    }

    .mobile-theme-btn.apply {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }

    .mobile-theme-btn:active {
        transform: scale(0.95);
    }
}

    /* ===== SMALL MOBILE STYLES (max-width: 480px) ===== */
    @media (max-width: 480px) {
        .mobile-theme-grid {
            grid-template-columns: 1fr;
        }

        .mobile-settings-actions {
            flex-direction: column;
        }

        .mobile-settings-actions .mobile-btn {
            width: 100%;
        }
    }
}





