@extends('layouts.vendor')

@section('title', 'Store Settings')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-settings-desktop.css') }}?v={{ time() }}" media="(min-width: 769px)">
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-settings-mobile.css') }}?v={{ time() }}" media="(max-width: 768px)">
@endpush

@push('page-scripts')
<script src="{{ asset('js/vendor/mobile-settings.js') }}?v={{ time() }}"></script>
@endpush

@push('scripts')
<script>
    function storeSettingsManager() {
        return {
            formData: {
                storeName: 'My Awesome Store',
                logoUrl: '',
                storeCategory: 'fashion',
                subcategory: '',
                businessType: 'product',
                businessDescription: 'Selling the coolest items on the planet.',
                addressLine1: '',
                addressLine2: '',
                city: '',
                state: '',
                pincode: '',
                country: 'India',
                website: '',
                facebook: '',
                twitter: '',
                instagram: '',
                linkedin: '',
                storeTheme: 'classic-comfort',
            },
            subcategories: [],
            showThemeMarketplace: false,
            selectedThemeCategory: 'all',

            init() {
                this.updateSubcategories();
                this.loadSettings();
            },

            updateSubcategories() {
                const categoryMap = {
                    electronics: [
                        { value: 'mobiles', label: 'Mobile Phones' },
                        { value: 'laptops', label: 'Laptops' },
                    ],
                    fashion: [
                        { value: 'mens', label: 'Men\'s Clothing' },
                        { value: 'womens', label: 'Women\'s Clothing' },
                    ],
                    home: [
                        { value: 'furniture', label: 'Furniture' },
                        { value: 'kitchenware', label: 'Kitchenware' },
                    ],
                    services: [
                        { value: 'consulting', label: 'Consulting' },
                        { value: 'repair', label: 'Repair Services' },
                    ]
                };
                this.subcategories = categoryMap[this.formData.storeCategory] || [];
            },

            loadSettings() {
                // In a real app, you'd fetch this from the server.
                const savedSettings = localStorage.getItem('vendorSettings');
                if (savedSettings) {
                    //this.formData = { ...this.formData, ...JSON.parse(savedSettings) };
                }
            },

            saveSettings() {
                localStorage.setItem('vendorSettings', JSON.stringify(this.formData));
                window.showToast('Settings saved successfully!', 'success');
            },

            uploadLogo(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.formData.logoUrl = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            },

            removeLogo() {
                this.formData.logoUrl = '';
            },

            openThemeMarketplace() {
                this.showThemeMarketplace = true;
            }
        }
    }
</script>
@endpush

@section('content')
<!-- Desktop View -->
<div class="settings-container" x-data="storeSettingsManager()">
    <!-- Header -->
    <div class="settings-header">
        <h1 class="settings-title">Store Settings</h1>
        <p class="settings-subtitle">Manage your store's appearance, information, and theme.</p>
    </div>

    <!-- Settings Cards -->
    <div class="settings-grid">
        <!-- Store Details Card -->
        <div class="settings-card">
            <div class="card-header">
                <h2 class="card-title">Store Details</h2>
                <p class="card-subtitle">Basic information about your store.</p>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Store Name</h3>
                        <p class="setting-description">This is the name displayed to customers.</p>
                    </div>
                    <div class="setting-control">
                        <input type="text" x-model="formData.storeName" class="form-input" placeholder="Enter store name">
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Store Logo</h3>
                        <p class="setting-description">Upload your brand logo to make your store recognizable.</p>
                    </div>
                    <div class="setting-control">
                        <div class="logo-upload-container">
                            <div class="logo-preview" x-show="formData.logoUrl">
                                <img :src="formData.logoUrl" alt="Store Logo" class="logo-image">
                                <button @click="removeLogo()" class="logo-remove-btn" type="button"><i class="fas fa-times"></i></button>
                            </div>
                            <div class="logo-upload" x-show="!formData.logoUrl">
                                <label for="logo-upload" class="upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Upload</span>
                                </label>
                                <input type="file" id="logo-upload" @change="uploadLogo($event)" class="visually-hidden" accept="image/*">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Business Description</h3>
                        <p class="setting-description">Tell customers about your business and what you offer.</p>
                    </div>
                    <div class="setting-control">
                        <textarea x-model="formData.businessDescription" class="form-textarea" placeholder="Describe your business"></textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Store Category Card -->
        <div class="settings-card">
            <div class="card-header">
                <h2 class="card-title">Store Category</h2>
                <p class="card-subtitle">Define what type of products or services you offer.</p>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Main Category</h3>
                        <p class="setting-description">Select the primary category for your business.</p>
                    </div>
                    <div class="setting-control">
                        <select x-model="formData.storeCategory" @change="updateSubcategories()" class="form-select">
                            <option value="">Select Category</option>
                            <option value="electronics">Electronics & Gadgets</option>
                            <option value="fashion">Fashion & Apparel</option>
                            <option value="home">Home & Kitchen</option>
                            <option value="services">Services</option>
                        </select>
                    </div>
                </div>
                
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Subcategory</h3>
                        <p class="setting-description">Select a more specific category for your products.</p>
                    </div>
                    <div class="setting-control">
                        <select x-model="formData.subcategory" class="form-select" :disabled="!subcategories.length">
                            <option value="">Select Subcategory</option>
                            <template x-for="subcategory in subcategories" :key="subcategory.value">
                                <option :value="subcategory.value" x-text="subcategory.label"></option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <!-- Store Theme Card -->
        <div class="settings-card">
            <div class="card-header">
                <h2 class="card-title">Store Theme</h2>
                <p class="card-subtitle">Choose how your store looks to customers</p>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Store Theme</h3>
                        <p class="setting-description">Select a theme that matches your brand identity</p>
                    </div>
                    <div class="setting-control">
                        <button class="btn btn-outline" @click="openThemeMarketplace()">
                            <i class="fas fa-paint-brush"></i> Choose Theme
                        </button>
                    </div>
                </div>
                
                <div class="gateway-notice">
                    <i class="fas fa-palette"></i>
                    <p>More themes coming soon!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="settings-actions">
        <button class="btn btn-secondary">Cancel</button>
        <button class="btn btn-primary" @click="saveSettings()">Save Settings</button>
    </div>

    <!-- Theme Marketplace Modal -->
    <div x-show="showThemeMarketplace" class="theme-marketplace-modal" @click.away="showThemeMarketplace = false" x-cloak>
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">Theme Marketplace</h3>
                <button class="modal-close" @click="showThemeMarketplace = false">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- Theme Categories Filter -->
                <div class="theme-filter">
                    <div class="theme-filter-label">Filter by business type:</div>
                    <div class="theme-filter-options">
                        <button class="theme-filter-btn" :class="{'active': selectedThemeCategory === 'all'}" @click="selectedThemeCategory = 'all'">All</button>
                        <button class="theme-filter-btn" :class="{'active': selectedThemeCategory === 'electronics'}" @click="selectedThemeCategory = 'electronics'">Electronics</button>
                        <button class="theme-filter-btn" :class="{'active': selectedThemeCategory === 'fashion'}" @click="selectedThemeCategory = 'fashion'">Fashion</button>
                        <button class="theme-filter-btn" :class="{'active': selectedThemeCategory === 'food'}" @click="selectedThemeCategory = 'food'">Food</button>
                        <button class="theme-filter-btn" :class="{'active': selectedThemeCategory === 'services'}" @click="selectedThemeCategory = 'services'">Services</button>
                    </div>
                </div>
                
                <!-- Theme Grid -->
                <div class="marketplace-theme-grid">
                    <template x-for="(theme, index) in filteredMarketplaceThemes" :key="theme.id">
                        <div class="marketplace-theme-card">
                            <div class="marketplace-theme-preview">
                                <img :src="theme.thumbnailUrl || '{{ asset('images/theme-placeholder.jpg') }}'" alt="Theme Preview" class="marketplace-theme-img">
                                <div class="marketplace-theme-badge" x-show="theme.popular">Popular</div>
                            </div>
                            <div class="marketplace-theme-info">
                                <h4 class="marketplace-theme-name" x-text="theme.name"></h4>
                                <div class="marketplace-theme-meta">
                                    <span class="marketplace-theme-category" x-text="theme.category"></span>
                                    <span class="marketplace-theme-separator">•</span>
                                    <div class="marketplace-theme-rating">
                                        <i class="fas fa-star"></i>
                                        <span x-text="theme.rating"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="marketplace-theme-buttons">
                                <button type="button" class="theme-btn preview" @click="previewTheme(theme.id)">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button type="button" class="theme-btn apply" @click="applyTheme(theme.id)">
                                    <i class="fas fa-check"></i> Apply
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" @click="showThemeMarketplace = false">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Mobile View -->
<div class="mobile-settings-app" x-data="storeSettingsManager()">
    <!-- Mobile Header -->
    <div class="mobile-settings-header">
        <h1 class="mobile-settings-title">Store Settings</h1>
    </div>



    <!-- Mobile Settings Content -->
    <div class="mobile-settings-content">
        <!-- Store Details Card -->
        <div class="mobile-settings-card">
            <div class="mobile-card-header">
                <div class="mobile-card-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="mobile-card-title">Store Details</div>
            </div>
            <div class="mobile-card-body">
                <div class="mobile-setting-item">
                    <div class="mobile-setting-info">
                        <div class="mobile-setting-name">Store Name</div>
                        <div class="mobile-setting-description">Display name for your store</div>
                    </div>
                    <div class="mobile-setting-control">
                        <input type="text" x-model="formData.storeName" class="mobile-form-input" placeholder="Enter store name">
                    </div>
                </div>

                <div class="mobile-setting-item">
                    <div class="mobile-setting-info">
                        <div class="mobile-setting-name">Store Logo</div>
                        <div class="mobile-setting-description">Upload your brand logo</div>
                    </div>
                    <div class="mobile-setting-control">
                        <div class="mobile-logo-upload-container">
                            <div class="mobile-logo-preview" x-show="formData.logoUrl">
                                <img :src="formData.logoUrl" alt="Store Logo" class="mobile-logo-image">
                                <button @click="removeLogo()" class="mobile-logo-remove-btn" type="button">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="mobile-logo-upload" x-show="!formData.logoUrl">
                                <label for="mobile-logo-upload" class="mobile-upload-label">
                                    <i class="fas fa-camera"></i>
                                    <span>Upload</span>
                                </label>
                                <input type="file" id="mobile-logo-upload" @change="uploadLogo($event)" class="visually-hidden" accept="image/*">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mobile-setting-item">
                    <div class="mobile-setting-info">
                        <div class="mobile-setting-name">Business Description</div>
                        <div class="mobile-setting-description">Tell customers about your business</div>
                    </div>
                    <div class="mobile-setting-control">
                        <textarea x-model="formData.businessDescription" class="mobile-form-textarea" placeholder="Describe your business"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Store Category Card -->
        <div class="mobile-settings-card">
            <div class="mobile-card-header">
                <div class="mobile-card-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="mobile-card-title">Store Category</div>
            </div>
            <div class="mobile-card-body">
                <div class="mobile-setting-item">
                    <div class="mobile-setting-info">
                        <div class="mobile-setting-name">Main Category</div>
                        <div class="mobile-setting-description">Primary business category</div>
                    </div>
                    <div class="mobile-setting-control">
                        <select x-model="formData.storeCategory" @change="updateSubcategories()" class="mobile-form-select">
                            <option value="">Select Category</option>
                            <option value="electronics">Electronics & Gadgets</option>
                            <option value="fashion">Fashion & Apparel</option>
                            <option value="home">Home & Kitchen</option>
                            <option value="services">Services</option>
                        </select>
                    </div>
                </div>

                <div class="mobile-setting-item">
                    <div class="mobile-setting-info">
                        <div class="mobile-setting-name">Subcategory</div>
                        <div class="mobile-setting-description">Specific product category</div>
                    </div>
                    <div class="mobile-setting-control">
                        <select x-model="formData.subcategory" class="mobile-form-select" :disabled="!subcategories.length">
                            <option value="">Select Subcategory</option>
                            <template x-for="subcategory in subcategories" :key="subcategory.value">
                                <option :value="subcategory.value" x-text="subcategory.label"></option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Store Theme Card -->
        <div class="mobile-settings-card">
            <div class="mobile-card-header">
                <div class="mobile-card-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="mobile-card-title">Store Theme</div>
            </div>
            <div class="mobile-card-body">
                <div class="mobile-setting-item">
                    <div class="mobile-setting-info">
                        <div class="mobile-setting-name">Store Theme</div>
                        <div class="mobile-setting-description">Choose your store's appearance</div>
                    </div>
                    <div class="mobile-setting-control">
                        <button class="mobile-btn mobile-btn-outline" @click="openThemeMarketplace()">
                            <i class="fas fa-paint-brush"></i>
                            Choose Theme
                        </button>
                    </div>
                </div>

                <div class="mobile-gateway-notice">
                    <i class="fas fa-palette"></i>
                    <p>More themes coming soon!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Actions -->
    <div class="mobile-settings-actions">
        <button class="mobile-btn mobile-btn-secondary">Cancel</button>
        <button class="mobile-btn mobile-btn-primary" @click="saveSettings()">
            <i class="fas fa-save"></i>
            Save Settings
        </button>
    </div>

    <!-- Mobile Theme Marketplace Modal -->
    <div x-show="showThemeMarketplace" class="mobile-theme-modal" @click.away="showThemeMarketplace = false" x-cloak>
        <div class="mobile-modal-container">
            <div class="mobile-modal-header">
                <h3 class="mobile-modal-title">Choose Theme</h3>
                <button class="mobile-modal-close" @click="showThemeMarketplace = false">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mobile-modal-body">
                <!-- Theme Categories Filter -->
                <div class="mobile-theme-filter">
                    <div class="mobile-filter-chips">
                        <button class="mobile-filter-chip" :class="{'active': selectedThemeCategory === 'all'}" @click="selectedThemeCategory = 'all'">All</button>
                        <button class="mobile-filter-chip" :class="{'active': selectedThemeCategory === 'electronics'}" @click="selectedThemeCategory = 'electronics'">Electronics</button>
                        <button class="mobile-filter-chip" :class="{'active': selectedThemeCategory === 'fashion'}" @click="selectedThemeCategory = 'fashion'">Fashion</button>
                        <button class="mobile-filter-chip" :class="{'active': selectedThemeCategory === 'food'}" @click="selectedThemeCategory = 'food'">Food</button>
                        <button class="mobile-filter-chip" :class="{'active': selectedThemeCategory === 'services'}" @click="selectedThemeCategory = 'services'">Services</button>
                    </div>
                </div>

                <!-- Mobile Theme Grid -->
                <div class="mobile-theme-grid">
                    <template x-for="(theme, index) in filteredMarketplaceThemes" :key="theme.id">
                        <div class="mobile-theme-card">
                            <div class="mobile-theme-preview">
                                <img :src="theme.thumbnailUrl || '{{ asset('images/theme-placeholder.jpg') }}'" alt="Theme Preview" class="mobile-theme-img">
                                <div class="mobile-theme-badge" x-show="theme.popular">Popular</div>
                            </div>
                            <div class="mobile-theme-info">
                                <h4 class="mobile-theme-name" x-text="theme.name"></h4>
                                <div class="mobile-theme-meta">
                                    <span class="mobile-theme-category" x-text="theme.category"></span>
                                    <span class="mobile-theme-separator">•</span>
                                    <div class="mobile-theme-rating">
                                        <i class="fas fa-star"></i>
                                        <span x-text="theme.rating"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="mobile-theme-buttons">
                                <button type="button" class="mobile-theme-btn preview" @click="previewTheme(theme.id)">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="mobile-theme-btn apply" @click="applyTheme(theme.id)">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <div class="mobile-modal-footer">
                <button class="mobile-btn mobile-btn-secondary" @click="showThemeMarketplace = false">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('vendorSettings', () => ({
        currentStep: 1,
        totalSteps: 5,
        stepTitles: ['Basic Info', 'Business Details', 'Location', 'Business Hours', 'Store Theme'],
        showThemeMarketplace: false,
        selectedThemeCategory: 'all',
        subcategories: [],
        businessDays: [
            { name: 'Monday', isOpen: true, openTime: '09:00', closeTime: '18:00' },
            { name: 'Tuesday', isOpen: true, openTime: '09:00', closeTime: '18:00' },
            { name: 'Wednesday', isOpen: true, openTime: '09:00', closeTime: '18:00' },
            { name: 'Thursday', isOpen: true, openTime: '09:00', closeTime: '18:00' },
            { name: 'Friday', isOpen: true, openTime: '09:00', closeTime: '18:00' },
            { name: 'Saturday', isOpen: true, openTime: '10:00', closeTime: '16:00' },
            { name: 'Sunday', isOpen: false, openTime: '10:00', closeTime: '14:00' }
        ],
        storeThemes: [
        formData: {
            storeName: 'My Awesome Store',
            logoUrl: '',
            storeCategory: 'fashion',
            subcategory: '',
            businessType: 'product',
            businessDescription: 'Selling the coolest items on the planet.',
            addressLine1: '',
            addressLine2: '',
            city: '',
            state: '',
            pincode: '',
            country: 'India',
            website: '',
            facebook: '',
            twitter: '',
            instagram: '',
            linkedin: '',
            storeTheme: 'classic-comfort',
        },

        storeThemes: [
            { id: 'classic-comfort', name: 'Classic Comfort', description: 'A clean and classic look.', preview_image: 'https://via.placeholder.com/300x200/f0f0f0/333?text=Classic' },
            { id: 'modern-minimal', name: 'Modern Minimal', description: 'Sleek, modern, and minimalist.', preview_image: 'https://via.placeholder.com/300x200/333/fff?text=Modern' },
        ],

        marketplaceThemes: [
            { id: 'classic-comfort', name: 'Classic Comfort', category: 'minimal', preview_image: 'https://via.placeholder.com/300x200/f0f0f0/333?text=Classic' },
            { id: 'modern-minimal', name: 'Modern Minimal', category: 'minimal', preview_image: 'https://via.placeholder.com/300x200/333/fff?text=Modern' },
            { id: 'vintage-vogue', name: 'Vintage Vogue', category: 'fashion', preview_image: 'https://via.placeholder.com/300x200/d9a97e/fff?text=Vintage' },
            { id: 'eco-chic', name: 'Eco Chic', category: 'fashion', preview_image: 'https://via.placeholder.com/300x200/a2d2a2/333?text=Eco' },
            { id: 'tech-titan', name: 'Tech Titan', category: 'electronics', preview_image: 'https://via.placeholder.com/300x200/4a4a4a/fff?text=Tech' },
            { id: 'gadget-grid', name: 'Gadget Grid', category: 'electronics', preview_image: 'https://via.placeholder.com/300x200/007bff/fff?text=Gadget' },
        ],

        init() {
            this.updateSubcategories();
            this.loadSettings();
        },

        get filteredMarketplaceThemes() {
            if (this.selectedThemeCategory === 'all') return this.marketplaceThemes;
            return this.marketplaceThemes.filter(theme => theme.category.toLowerCase() === this.selectedThemeCategory);
        },

        loadSettings() {
            // In a real app, you'd fetch this from the server.
            const savedSettings = localStorage.getItem('vendorSettings');
            if (savedSettings) {
                //this.formData = { ...this.formData, ...JSON.parse(savedSettings) };
            }
        },

        saveSettings() {
            if (!this.validateForm()) return;
            localStorage.setItem('vendorSettings', JSON.stringify(this.formData));
            window.showToast('Settings saved successfully!', 'success');
        },

        updateSubcategories() {
            const categoryMap = {
                electronics: [
                    { value: 'mobiles', label: 'Mobile Phones' },
                    { value: 'laptops', label: 'Laptops' },
                ],
                fashion: [
                    { value: 'mens', label: 'Men\'s Clothing' },
                    { value: 'womens', label: 'Women\'s Clothing' },
                ],
                home: [
                    { value: 'furniture', label: 'Furniture' },
                    { value: 'kitchenware', label: 'Kitchenware' },
                ],
                services: [
                    { value: 'consulting', label: 'Consulting' },
                    { value: 'repair', label: 'Repair Services' },
                ]
            };
            this.subcategories = categoryMap[this.formData.storeCategory] || [];
            if (!this.subcategories.find(s => s.value === this.formData.subcategory)) {
                this.formData.subcategory = '';
            }
        },

        uploadLogo(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.formData.logoUrl = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        },

        removeLogo() {
            this.formData.logoUrl = '';
        },

        detectLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(position => {
                    // You would use a geocoding service here
                    this.formData.city = 'Mumbai'; // Mock data
                    this.formData.state = 'Maharashtra';
                    window.showToast('Location detected!', 'success');
                }, error => {
                    window.showToast('Could not detect location.', 'error');
                });
            }
        },

        openThemeMarketplace() {
            this.showThemeMarketplace = true;
        },

        previewTheme(themeId) {
            const theme = this.marketplaceThemes.find(t => t.id === themeId);
            if (theme) {
                // Temporarily apply styles for preview
            }
        },

        applyTheme(themeId) {
            this.formData.storeTheme = themeId;
            this.showThemeMarketplace = false;
            window.showToast('Theme applied successfully!', 'success');
        },

        validateForm() {
            if (!this.formData.storeName || !this.formData.storeCategory) {
                window.showToast('Please fill all required fields.', 'error');
                return false;
            }
            return true;
        }
    }));
});
</script>
@endpush
